import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { useAuthContext } from '@/contexts/AuthContext'

// Types for our financial data
export interface Saldo {
  id: number
  created_at: string
  nome_da_conta: string
  saldo: number
  user_id: number
}

export interface Entrada {
  id: number
  created_at: string
  nome_entrada: string
  valor_entrada: number
  user_id: number
  data_entrada: string
}

export interface Gasto {
  id: number
  created_at: string
  nome_gasto: string
  valor_gasto: number
  user_id: number
  data_gasto: string
}

export const useFinancialData = () => {
  const { user } = useAuthContext()
  const [saldos, setSaldos] = useState<Saldo[]>([])
  const [entradas, setEntradas] = useState<Entrada[]>([])
  const [gastos, setGastos] = useState<Gasto[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Get user_id from users table using auth_uuid
  const [userId, setUserId] = useState<number | null>(null)

  // Add timeout for loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (loading) {
        console.log('Loading timeout reached')
        setError('Timeout ao carregar dados')
        setLoading(false)
      }
    }, 10000) // 10 seconds timeout

    return () => clearTimeout(timeout)
  }, [loading])

  useEffect(() => {
    const getUserId = async () => {
      if (!user?.id) return

      try {
        const { data, error } = await supabase
          .from('users')
          .select('id')
          .eq('auth_uuid', user.id)
          .single()

        if (error) {
          setError(`Erro ao buscar dados do usuário: ${error.message}`)
          return
        }

        setUserId(data.id)
      } catch (err) {
        setError(`Erro inesperado: ${err}`)
      }
    }

    getUserId()
  }, [user?.id])

  useEffect(() => {
    if (!userId) return

    const fetchFinancialData = async () => {
      setLoading(true)
      setError(null)

      try {
        // Fetch saldos
        const { data: saldosData, error: saldosError } = await supabase
          .from('saldos')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })

        if (saldosError) throw saldosError

        // Fetch entradas
        const { data: entradasData, error: entradasError } = await supabase
          .from('entradas')
          .select('*')
          .eq('user_id', userId)
          .order('data_entrada', { ascending: false })

        if (entradasError) throw entradasError

        // Fetch gastos
        const { data: gastosData, error: gastosError } = await supabase
          .from('gastos')
          .select('*')
          .eq('user_id', userId)
          .order('data_gasto', { ascending: false })

        if (gastosError) throw gastosError

        setSaldos(saldosData || [])
        setEntradas(entradasData || [])
        setGastos(gastosData || [])
      } catch (err: any) {
        setError(err.message || 'Erro ao carregar dados financeiros')
      } finally {
        setLoading(false)
      }
    }

    fetchFinancialData()
  }, [userId])

  // Calculate totals
  const totalSaldo = saldos.reduce((sum, saldo) => sum + saldo.saldo, 0)
  const totalEntradas = entradas.reduce((sum, entrada) => sum + entrada.valor_entrada, 0)
  const totalGastos = gastos.reduce((sum, gasto) => sum + gasto.valor_gasto, 0)

  // Get recent transactions (last 5 of each)
  const recentEntradas = entradas.slice(0, 5)
  const recentGastos = gastos.slice(0, 5)

  // Calculate monthly data (current month)
  const currentMonth = new Date().getMonth() + 1
  const currentYear = new Date().getFullYear()

  const monthlyEntradas = entradas.filter(entrada => {
    const entradaDate = new Date(entrada.data_entrada)
    return entradaDate.getMonth() + 1 === currentMonth && entradaDate.getFullYear() === currentYear
  })

  const monthlyGastos = gastos.filter(gasto => {
    const gastoDate = new Date(gasto.data_gasto)
    return gastoDate.getMonth() + 1 === currentMonth && gastoDate.getFullYear() === currentYear
  })

  const monthlyTotalEntradas = monthlyEntradas.reduce((sum, entrada) => sum + entrada.valor_entrada, 0)
  const monthlyTotalGastos = monthlyGastos.reduce((sum, gasto) => sum + gasto.valor_gasto, 0)

  return {
    saldos,
    entradas,
    gastos,
    loading,
    error,
    totalSaldo,
    totalEntradas,
    totalGastos,
    recentEntradas,
    recentGastos,
    monthlyTotalEntradas,
    monthlyTotalGastos,
    userId
  }
}
