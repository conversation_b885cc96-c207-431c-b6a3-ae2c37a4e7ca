
import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { 
  MessageCircle, 
  User, 
  Settings, 
  LogOut, 
  PieChart, 
  TrendingUp, 
  DollarSign,
  Calendar,
  Bell,
  Search,
  Wallet,
  ArrowUpCircle,
  ArrowDownCircle
} from "lucide-react";
import { Button } from "@/components/ui/button";

const Dashboard = () => {
  const [user] = useState({ name: "Admin", email: "<EMAIL>" });

  const handleLogout = () => {
    // Aqui você adicionaria a lógica de logout
    console.log("Logout");
    window.location.href = "/";
  };

  const stats = [
    { title: "Receitas", value: "R$ 12.450", change: "+12%", icon: TrendingUp, color: "text-green-600" },
    { title: "Despesas", value: "R$ 8.320", change: "-5%", icon: DollarSign, color: "text-red-600" },
    { title: "Economia", value: "R$ 4.130", change: "+18%", icon: <PERSON><PERSON><PERSON>, color: "text-blue-600" },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center gap-3 group">
              <div className="relative">
                <div className="absolute inset-0 bg-lia-green/20 rounded-full blur-sm group-hover:blur-md transition-all duration-300"></div>
                <MessageCircle className="relative w-8 h-8 text-lia-green group-hover:scale-110 transition-transform duration-300" />
              </div>
              <span className="text-xl font-black tracking-tight text-gray-900 group-hover:text-lia-green transition-colors duration-300">
                LIA
              </span>
            </Link>

            {/* Search */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar transações..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lia-green focus:border-lia-green"
                />
              </div>
            </div>

            {/* User Menu */}
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="icon">
                <Bell className="h-5 w-5" />
              </Button>
              <div className="flex items-center gap-3">
                <div className="text-right hidden sm:block">
                  <p className="text-sm font-medium text-gray-900">{user.name}</p>
                  <p className="text-xs text-gray-500">{user.email}</p>
                </div>
                <Button variant="ghost" size="icon">
                  <User className="h-5 w-5" />
                </Button>
                <Button variant="ghost" size="icon" onClick={handleLogout}>
                  <LogOut className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Bem-vindo de volta, {user.name}! 👋
          </h1>
          <p className="text-gray-600">
            Aqui está um resumo das suas finanças hoje.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  <p className={`text-sm font-medium ${stat.color}`}>{stat.change} vs mês passado</p>
                </div>
                <div className={`p-3 rounded-full bg-gray-50`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Recent Transactions */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Transações Recentes</h2>
              <Button variant="outline" size="sm">Ver Todas</Button>
            </div>
            <div className="space-y-4">
              {[
                { name: "Supermercado", amount: "-R$ 250,00", date: "Hoje", category: "Alimentação" },
                { name: "Salário", amount: "+R$ 4.500,00", date: "Ontem", category: "Receita" },
                { name: "Conta de Luz", amount: "-R$ 180,00", date: "2 dias", category: "Contas" },
              ].map((transaction, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-lia-green/10 rounded-full flex items-center justify-center">
                      <DollarSign className="h-5 w-5 text-lia-green" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{transaction.name}</p>
                      <p className="text-sm text-gray-500">{transaction.category} • {transaction.date}</p>
                    </div>
                  </div>
                  <p className={`font-semibold ${transaction.amount.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                    {transaction.amount}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Ações Rápidas</h2>
            <div className="grid grid-cols-2 gap-4">
              <Button className="h-20 flex-col gap-2 bg-gradient-to-r from-lia-green to-emerald-500 hover:from-emerald-600 hover:to-lia-green">
                <Wallet className="h-5 w-5" />
                Novo Saldo
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2">
                <ArrowUpCircle className="h-5 w-5" />
                Nova Entrada
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2">
                <ArrowDownCircle className="h-5 w-5" />
                Novo Gasto
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Bell className="h-5 w-5" />
                Novo Lembrete
              </Button>
            </div>
          </div>
        </div>

        {/* Chat with LIA */}
        <div className="bg-gradient-to-r from-lia-green to-emerald-500 rounded-xl p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold mb-2">Converse com a LIA</h2>
              <p className="text-green-100">
                Tire suas dúvidas financeiras e receba conselhos personalizados
              </p>
            </div>
            <Button variant="secondary" className="bg-white text-lia-green hover:bg-gray-100">
              <MessageCircle className="h-4 w-4 mr-2" />
              Iniciar Chat
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
