import { useState, useEffect } from 'react'
import { supabase, type AuthUser } from '@/lib/supabase'
import { User } from '@supabase/supabase-js'

export const useAuth = () => {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (session?.user) {
          await loadUserData(session.user)
        }
      } catch (error) {
        setUser(null)
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      try {
        if (session?.user) {
          await loadUserData(session.user)
        } else {
          setUser(null)
        }
      } catch (error) {
        setUser(null)
      } finally {
        setLoading(false)
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  const loadUserData = async (authUser: User) => {
    // SEMPRE definir o usuário primeiro, mesmo que básico
    setUser({
      id: authUser.id,
      email: authUser.email || ''
    })

    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout loading user data')), 3000)
      )

      const queryPromise = supabase
        .from('users')
        .select('numero, nome')
        .eq('auth_uuid', authUser.id)
        .single()

      const { data: userData, error } = await Promise.race([queryPromise, timeoutPromise]) as any

      if (error) {
        // Usuário básico já foi definido acima
        return
      }

      // Atualizar com dados completos
      setUser({
        id: authUser.id,
        email: authUser.email || '',
        numero: userData?.numero,
        nome: userData?.nome
      })
    } catch (error) {
      // Usuário básico já foi definido acima - isso é OK!
    }
  }

  const signInWithNumber = async (numero: number, password: string) => {
    try {
      const phoneNumber = numero.toString()

      // Get email by phone
      const { data: emailData, error: emailError } = await supabase
        .rpc('get_user_email_by_phone', { user_phone: phoneNumber })

      if (emailError || !emailData) {
        throw new Error('Número não encontrado')
      }

      // Sign in
      const { data, error } = await supabase.auth.signInWithPassword({
        email: emailData,
        password: password
      })

      if (error) {
        throw new Error('Credenciais inválidas')
      }

      return { data, error: null }
    } catch (error: any) {
      return { data: null, error }
    }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    setUser(null)
    return { error }
  }

  return {
    user,
    loading,
    signInWithNumber,
    signOut
  }
}
