import { useState, useEffect } from 'react'
import { supabase, type AuthUser } from '@/lib/supabase'
import { User } from '@supabase/supabase-js'

export const useAuth = () => {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      if (session?.user) {
        await loadUserData(session.user)
      }
      setLoading(false)
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        await loadUserData(session.user)
      } else {
        setUser(null)
      }
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const loadUserData = async (authUser: User) => {
    try {
      const { data: userData, error } = await supabase
        .from('users')
        .select('numero')
        .eq('id', authUser.id)
        .single()

      if (error) {
        console.error('Error loading user data:', error)
        setUser({
          id: authUser.id,
          email: authUser.email || ''
        })
        return
      }

      setUser({
        id: authUser.id,
        email: authUser.email || '',
        numero: userData?.numero
      })
    } catch (error) {
      console.error('Error in loadUserData:', error)
      setUser({
        id: authUser.id,
        email: authUser.email || ''
      })
    }
  }

  const signInWithNumber = async (numero: number, password: string) => {
    try {
      // Approach: Use a function or RPC call to get email by numero
      // For now, we'll use a simple approach where email follows a pattern
      // You'll need to tell me your actual email to make this work

      // Try to sign in with the email pattern
      const { data, error } = await supabase.auth.signInWithPassword({
        email: `user${numero}@lia.com`, // You'll need to adjust this pattern
        password: password
      })

      if (error) {
        throw new Error('Credenciais inválidas. Verifique seu número e senha.')
      }

      return { data, error: null }
    } catch (error: any) {
      return { data: null, error }
    }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    if (!error) {
      setUser(null)
    }
    return { error }
  }

  return {
    user,
    loading,
    signInWithNumber,
    signOut
  }
}
