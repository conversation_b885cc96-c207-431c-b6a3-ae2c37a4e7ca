import { useState, useEffect } from 'react'
import { supabase, type AuthUser } from '@/lib/supabase'
import { User } from '@supabase/supabase-js'

export const useAuth = () => {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      if (session?.user) {
        await loadUserData(session.user)
      }
      setLoading(false)
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        await loadUserData(session.user)
      } else {
        setUser(null)
      }
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const loadUserData = async (authUser: User) => {
    try {
      const { data: userData, error } = await supabase
        .from('users')
        .select('numero, nome')
        .eq('auth_uuid', authUser.id)
        .single()

      if (error) {
        console.error('Error loading user data:', error)
        setUser({
          id: authUser.id,
          email: authUser.email || ''
        })
        return
      }

      setUser({
        id: authUser.id,
        email: authUser.email || '',
        numero: userData?.numero,
        nome: userData?.nome
      })
    } catch (error) {
      console.error('Error in loadUserData:', error)
      setUser({
        id: authUser.id,
        email: authUser.email || ''
      })
    }
  }

  const signInWithNumber = async (numero: number, password: string) => {
    try {
      // First, find the user by numero to get their auth_uuid
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('auth_uuid')
        .eq('numero', numero)
        .single()

      if (userError || !userData?.auth_uuid) {
        throw new Error('Número não encontrado')
      }

      // Now we need to get the email from auth.users
      // Since we can't query auth.users directly, we'll use a different approach
      // We'll create a database function to help us

      // For now, let's try a workaround using the RPC function
      const { data: emailData, error: emailError } = await supabase
        .rpc('get_user_email_by_uuid', { user_uuid: userData.auth_uuid })

      if (emailError || !emailData) {
        throw new Error('Erro ao buscar dados do usuário')
      }

      // Sign in with the found email and password
      const { data, error } = await supabase.auth.signInWithPassword({
        email: emailData,
        password: password
      })

      if (error) {
        throw new Error('Credenciais inválidas. Verifique sua senha.')
      }

      return { data, error: null }
    } catch (error: any) {
      return { data: null, error }
    }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    if (!error) {
      setUser(null)
    }
    return { error }
  }

  return {
    user,
    loading,
    signInWithNumber,
    signOut
  }
}
