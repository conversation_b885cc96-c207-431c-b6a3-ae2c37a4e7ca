import { useState, useEffect } from 'react'
import { supabase, type AuthUser } from '@/lib/supabase'
import { User } from '@supabase/supabase-js'

export const useAuth = () => {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      console.log('🔍 Getting initial session...')
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (session?.user) {
          console.log('🔍 Found session, loading user data...')
          await loadUserData(session.user)
        } else {
          console.log('❌ No session found')
        }
      } catch (error) {
        console.error('❌ Error getting session:', error)
        setUser(null)
      } finally {
        console.log('✅ Setting loading to false')
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔍 Auth state change:', event)
      try {
        if (session?.user) {
          console.log('🔍 Session user found, loading data...')
          await loadUserData(session.user)
        } else {
          console.log('❌ No session user')
          setUser(null)
        }
      } catch (error) {
        console.error('❌ Error in auth state change:', error)
        setUser(null)
      } finally {
        console.log('✅ Setting loading to false (auth change)')
        setLoading(false)
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  const loadUserData = async (authUser: User) => {
    console.log('🔍 loadUserData for auth user:', authUser.id)

    // SEMPRE definir o usuário primeiro, mesmo que básico
    setUser({
      id: authUser.id,
      email: authUser.email || ''
    })
    console.log('✅ Set basic user data immediately')

    try {
      console.log('🔍 Fetching extended user data from users table...')

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout loading user data')), 3000)
      )

      const queryPromise = supabase
        .from('users')
        .select('numero, nome')
        .eq('auth_uuid', authUser.id)
        .single()

      const { data: userData, error } = await Promise.race([queryPromise, timeoutPromise]) as any

      if (error) {
        console.error('❌ Error loading extended user data:', error)
        console.log('❌ Error details:', error.message, error.code)
        // Usuário básico já foi definido acima
        return
      }

      console.log('🔍 Raw userData from database:', userData)

      // Atualizar com dados completos
      setUser({
        id: authUser.id,
        email: authUser.email || '',
        numero: userData?.numero,
        nome: userData?.nome
      })
      console.log('✅ Updated to full user data:', userData?.nome, userData?.numero)
    } catch (error) {
      console.error('❌ Exception in loadUserData:', error)
      console.log('❌ Exception details:', error)
      // Usuário básico já foi definido acima - isso é OK!
      console.log('✅ Continuing with basic user data (timeout/error is OK)')
    }
  }

  const signInWithNumber = async (numero: number, password: string) => {
    try {
      const phoneNumber = numero.toString()

      // Get email by phone
      const { data: emailData, error: emailError } = await supabase
        .rpc('get_user_email_by_phone', { user_phone: phoneNumber })

      if (emailError || !emailData) {
        throw new Error('Número não encontrado')
      }

      // Sign in
      const { data, error } = await supabase.auth.signInWithPassword({
        email: emailData,
        password: password
      })

      if (error) {
        throw new Error('Credenciais inválidas')
      }

      return { data, error: null }
    } catch (error: any) {
      return { data: null, error }
    }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    setUser(null)
    return { error }
  }

  return {
    user,
    loading,
    signInWithNumber,
    signOut
  }
}
