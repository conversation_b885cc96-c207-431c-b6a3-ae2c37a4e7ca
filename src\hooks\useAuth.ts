import { useState, useEffect } from 'react'
import { supabase, type AuthUser } from '@/lib/supabase'
import { User } from '@supabase/supabase-js'

export const useAuth = () => {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Session error:', error)
          // Clear corrupted session
          await supabase.auth.signOut()
          setUser(null)
          setLoading(false)
          return
        }

        if (session?.user) {
          await loadUserData(session.user)
        }
        setLoading(false)
      } catch (err) {
        console.error('Error getting session:', err)
        // Clear any corrupted data
        await supabase.auth.signOut()
        setUser(null)
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state change:', event, session?.user?.id)

      if (event === 'SIGNED_OUT') {
        setUser(null)
        setLoading(false)
        return
      }

      if (session?.user) {
        await loadUserData(session.user)
      } else {
        setUser(null)
      }
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const loadUserData = async (authUser: User) => {
    try {
      const { data: userData, error } = await supabase
        .from('users')
        .select('numero, nome')
        .eq('auth_uuid', authUser.id)
        .single()

      if (error) {
        console.error('Error loading user data:', error)
        setUser({
          id: authUser.id,
          email: authUser.email || ''
        })
        return
      }

      setUser({
        id: authUser.id,
        email: authUser.email || '',
        numero: userData?.numero,
        nome: userData?.nome
      })
    } catch (error) {
      console.error('Error in loadUserData:', error)
      setUser({
        id: authUser.id,
        email: authUser.email || ''
      })
    }
  }

  const signInWithNumber = async (numero: number, password: string) => {
    try {
      // Clear any existing session first
      await supabase.auth.signOut()

      // Use the phone number as string without adding +
      const phoneNumber = numero.toString()

      // Use the RPC function to get email by phone
      const { data: emailData, error: emailError } = await supabase
        .rpc('get_user_email_by_phone', { user_phone: phoneNumber })

      if (emailError || !emailData) {
        throw new Error('Número não encontrado')
      }

      // Sign in with the found email and password
      const { data, error } = await supabase.auth.signInWithPassword({
        email: emailData,
        password: password
      })

      if (error) {
        throw new Error('Credenciais inválidas. Verifique sua senha.')
      }

      return { data, error: null }
    } catch (error: any) {
      return { data: null, error }
    }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    setUser(null) // Always clear user state

    // Clear any cached data
    localStorage.clear()
    sessionStorage.clear()

    return { error }
  }

  const forceSignOut = async () => {
    // Force complete logout
    await supabase.auth.signOut()
    setUser(null)
    localStorage.clear()
    sessionStorage.clear()

    // Reload page to ensure clean state
    window.location.href = '/login'
  }

  return {
    user,
    loading,
    signInWithNumber,
    signOut
  }
}
