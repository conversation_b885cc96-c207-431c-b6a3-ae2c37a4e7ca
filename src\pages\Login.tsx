
import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { MessageCircle, Hash, Lock, Eye, EyeOff, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";

const Login = () => {
  const [numero, setNumero] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { user, signInWithNumber, signOut } = useAuth();

  // Force logout when accessing login page
  useEffect(() => {
    const forceLogout = async () => {
      if (user) {
        console.log('Forcing logout to ensure clean login');
        await signOut();
        // Clear everything
        localStorage.clear();
        sessionStorage.clear();
      }
    };

    forceLogout();
  }, []);

  // Redirect if already logged in (after the forced logout check)
  useEffect(() => {
    if (user) {
      console.log('User already logged in, redirecting to dashboard');
      navigate("/dashboard", { replace: true });
    }
  }, [user, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      const numeroInt = parseInt(numero);
      if (isNaN(numeroInt)) {
        setError("Por favor, digite um número válido");
        return;
      }

      const { error } = await signInWithNumber(numeroInt, password);

      if (error) {
        setError(error.message || "Erro ao fazer login");
      } else {
        navigate("/dashboard");
      }
    } catch (err: any) {
      setError(err.message || "Erro inesperado");
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        {/* Logo */}
        <div className="text-center">
          <Link to="/" className="inline-flex items-center gap-3 group">
            <div className="relative">
              <div className="absolute inset-0 bg-lia-green/20 rounded-full blur-sm group-hover:blur-md transition-all duration-300"></div>
              <MessageCircle className="relative w-10 h-10 text-lia-green group-hover:scale-110 transition-transform duration-300" />
            </div>
            <span className="text-2xl font-black tracking-tight text-gray-900 group-hover:text-lia-green transition-colors duration-300">
              LIA
            </span>
          </Link>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Bem-vindo de volta!
          </h2>
          <p className="mt-2 text-gray-600">
            Faça login para acessar sua conta
          </p>
        </div>

        {/* Form */}
        <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
                {error}
              </div>
            )}

            {/* Número */}
            <div>
              <label htmlFor="numero" className="block text-sm font-medium text-gray-700 mb-2">
                Número
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Hash className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="numero"
                  type="number"
                  value={numero}
                  onChange={(e) => setNumero(e.target.value)}
                  className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lia-green focus:border-lia-green transition-colors"
                  placeholder="Digite seu número"
                  required
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Password */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Senha
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lia-green focus:border-lia-green transition-colors"
                  placeholder="Digite sua senha"
                  required
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  )}
                </button>
              </div>
            </div>

            {/* Forgot Password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-lia-green focus:ring-lia-green border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                  Lembrar de mim
                </label>
              </div>
              <a href="#" className="text-sm text-lia-green hover:text-emerald-500 font-medium">
                Esqueceu a senha?
              </a>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-lia-green to-emerald-500 hover:from-emerald-600 hover:to-lia-green text-white font-semibold py-3 px-4 rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Entrando...
                </>
              ) : (
                "Entrar"
              )}
            </Button>
          </form>

          {/* Instructions */}
          <div className="mt-6 p-4 bg-lia-green/5 border border-lia-green/20 rounded-lg">
            <p className="text-sm text-lia-green text-center">
              <strong>Como fazer login:</strong><br />
              Use seu número cadastrado e a senha da sua conta
            </p>
          </div>

          {/* Back to home */}
          <div className="mt-6 text-center">
            <Link
              to="/"
              className="text-sm text-gray-600 hover:text-lia-green font-medium"
            >
              ← Voltar para o início
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
